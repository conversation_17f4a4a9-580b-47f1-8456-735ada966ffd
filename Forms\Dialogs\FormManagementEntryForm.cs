using System;
using System.Diagnostics;
using System.Drawing;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using ProManage.Modules.Data.FormManagement;
using ProManage.Modules.Helpers.FormManagement;
using ProManage.Modules.Models.FormManagement;
using ProManage.Modules.Services;
using ProManage.Modules.UI;

namespace ProManage.Forms.Dialogs
{
    /// <summary>
    /// Form entry dialog for creating and editing forms
    /// Provides validation and CRUD operations for form management
    /// </summary>
    public partial class FormManagementEntryForm : XtraForm
    {
        #region Properties

        /// <summary>
        /// Form model being edited (null for new forms)
        /// </summary>
        private FormManagementModel _formModel;

        /// <summary>
        /// Indicates if this is an edit operation
        /// </summary>
        private bool _isEditMode;

        #endregion

        #region Constructors

        /// <summary>
        /// Constructor for creating new forms
        /// </summary>
        public FormManagementEntryForm()
        {
            InitializeComponent();
            _isEditMode = false;
            _formModel = new FormManagementModel();
            InitializeForm();
        }

        /// <summary>
        /// Constructor for editing existing forms
        /// </summary>
        /// <param name="formToEdit">Form model to edit</param>
        public FormManagementEntryForm(FormManagementModel formToEdit)
        {
            InitializeComponent();
            _isEditMode = true;
            _formModel = formToEdit?.Clone() ?? new FormManagementModel();
            InitializeForm();
        }

        #endregion

        #region Form Initialization

        /// <summary>
        /// Initializes the form controls and data
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                Debug.WriteLine($"=== FormManagementEntryForm.InitializeForm: Edit Mode = {_isEditMode} ===");

                // Set form title
                this.Text = _isEditMode ? "Edit Form" : "Create New Form";

                // Apply ProManage design standards
                ApplyDesignStandards();

                // Populate category dropdown
                FormManagementHelper.PopulateCategoryComboBox(cboCategory);

                // Load form data if editing
                if (_isEditMode && _formModel != null)
                {
                    LoadFormData();
                }

                // Set initial focus
                txtFormName.Focus();

                Debug.WriteLine("FormManagementEntryForm initialized successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing FormManagementEntryForm: {ex.Message}");
                MessageBox.Show($"Error initializing form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Applies ProManage design standards to controls
        /// </summary>
        private void ApplyDesignStandards()
        {
            // Background color
            this.BackColor = Color.FromArgb(245, 245, 245);

            // Font settings
            var standardFont = new Font("Segoe UI", 9F);
            this.Font = standardFont;

            // Control styling
            foreach (Control control in this.Controls)
            {
                if (control is TextEdit || control is ComboBoxEdit || control is CheckEdit)
                {
                    control.Font = standardFont;
                }
            }
        }

        /// <summary>
        /// Loads existing form data into controls
        /// </summary>
        private void LoadFormData()
        {
            try
            {
                if (_formModel == null) return;

                txtFormName.Text = _formModel.FormName ?? "";
                txtDisplayName.Text = _formModel.DisplayName ?? "";
                cboCategory.Text = _formModel.Category ?? "";
                chkIsActive.Checked = _formModel.IsActive;

                Debug.WriteLine($"Form data loaded: {_formModel.FormName}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading form data: {ex.Message}");
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles form name text changes for validation
        /// </summary>
        private void TxtFormName_TextChanged(object sender, EventArgs e)
        {
            try
            {
                ValidateFormName();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in TxtFormName_TextChanged: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles OK button click - saves the form
        /// </summary>
        private void BtnOK_Click(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("=== OK button clicked ===");

                if (ValidateForm())
                {
                    SaveForm();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in OK button click: {ex.Message}");
                MessageBox.Show($"Error saving form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Cancel button click - closes without saving
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("=== Cancel button clicked ===");
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Cancel button click: {ex.Message}");
            }
        }

        #endregion

        #region Validation

        /// <summary>
        /// Validates the form name field
        /// </summary>
        private void ValidateFormName()
        {
            try
            {
                string formName = txtFormName.Text?.Trim();
                
                if (string.IsNullOrWhiteSpace(formName))
                {
                    SetFieldError(txtFormName, "Form name is required");
                    return;
                }

                if (!FormManagementModel.IsValidFormNameFormat(formName))
                {
                    SetFieldError(txtFormName, "Form name must start with a letter and contain only letters, numbers, and underscores");
                    return;
                }

                // Check availability (exclude current form if editing)
                int? excludeId = _isEditMode ? _formModel?.FormId : null;
                if (!FormManagementHelper.ValidateFormNameAvailability(formName, excludeId))
                {
                    SetFieldError(txtFormName, "Form name already exists");
                    return;
                }

                ClearFieldError(txtFormName);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error validating form name: {ex.Message}");
            }
        }

        /// <summary>
        /// Validates the entire form
        /// </summary>
        /// <returns>True if valid</returns>
        private bool ValidateForm()
        {
            try
            {
                bool isValid = true;

                // Validate form name
                string formName = txtFormName.Text?.Trim();
                if (string.IsNullOrWhiteSpace(formName))
                {
                    SetFieldError(txtFormName, "Form name is required");
                    isValid = false;
                }
                else if (!FormManagementModel.IsValidFormNameFormat(formName))
                {
                    SetFieldError(txtFormName, "Invalid form name format");
                    isValid = false;
                }
                else
                {
                    // Check uniqueness
                    int? excludeId = _isEditMode ? _formModel?.FormId : null;
                    if (!FormManagementHelper.ValidateFormNameAvailability(formName, excludeId))
                    {
                        SetFieldError(txtFormName, "Form name already exists");
                        isValid = false;
                    }
                    else
                    {
                        ClearFieldError(txtFormName);
                    }
                }

                // Validate display name length
                string displayName = txtDisplayName.Text?.Trim();
                if (!string.IsNullOrEmpty(displayName) && displayName.Length > 255)
                {
                    SetFieldError(txtDisplayName, "Display name cannot exceed 255 characters");
                    isValid = false;
                }
                else
                {
                    ClearFieldError(txtDisplayName);
                }

                // Validate category length
                string category = cboCategory.Text?.Trim();
                if (!string.IsNullOrEmpty(category) && category.Length > 100)
                {
                    SetFieldError(cboCategory, "Category cannot exceed 100 characters");
                    isValid = false;
                }
                else
                {
                    ClearFieldError(cboCategory);
                }

                return isValid;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error validating form: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Sets error state for a field
        /// </summary>
        private void SetFieldError(Control control, string message)
        {
            control.BackColor = Color.FromArgb(255, 230, 230);
            // You could also use an ErrorProvider here
        }

        /// <summary>
        /// Clears error state for a field
        /// </summary>
        private void ClearFieldError(Control control)
        {
            control.BackColor = Color.White;
        }

        #endregion

        #region Save Operations

        /// <summary>
        /// Saves the form to database
        /// </summary>
        private void SaveForm()
        {
            ProgressIndicatorService.Instance.ShowProgress();

            try
            {
                // Update model with form data
                _formModel.FormName = txtFormName.Text?.Trim();
                _formModel.DisplayName = string.IsNullOrWhiteSpace(txtDisplayName.Text) ? null : txtDisplayName.Text.Trim();
                _formModel.Category = string.IsNullOrWhiteSpace(cboCategory.Text) ? null : cboCategory.Text.Trim();
                _formModel.IsActive = chkIsActive.Checked;

                FormManagementModel savedForm;

                if (_isEditMode)
                {
                    savedForm = FormManagementRepository.UpdateForm(_formModel);
                    Debug.WriteLine($"Form updated: {savedForm.FormId}");
                }
                else
                {
                    savedForm = FormManagementRepository.CreateForm(_formModel);
                    Debug.WriteLine($"Form created: {savedForm.FormId}");
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error saving form: {ex.Message}");
                MessageBox.Show($"Error saving form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        #endregion
    }
}
