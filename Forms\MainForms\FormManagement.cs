using System;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using ProManage.Modules.Helpers.FormManagement;
using ProManage.Modules.Models.FormManagement;
using ProManage.Forms.ReusableForms;

namespace ProManage.Forms.MainForms
{
    /// <summary>
    /// Form Management - Main form for managing system forms
    /// Provides CRUD operations for forms with DevExpress grid interface
    /// </summary>
    public partial class FormManagement : XtraForm
    {
        #region Properties

        /// <summary>
        /// DataTable for grid binding
        /// </summary>
        public DataTable GridDataTable { get; set; }

        /// <summary>
        /// MenuRibbon user control for consistent interface
        /// </summary>
        private MenuRibbon menuRibbon;

        #endregion

        #region Constructor

        public FormManagement()
        {
            InitializeComponent();

            // TEMPORARILY DISABLED: MenuRibbon UC is causing grid loading issues
            // Only initialize MenuRibbon at runtime, not in designer
            //if (!DesignMode)
            //{
            //    InitializeMenuRibbon();
            //}
        }

        #endregion

        #region Form Events

        private void FormManagement_Load(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("=== FormManagement_Load: Starting ===");

                // Test database connection first
                TestDatabaseConnection();

                // Initialize the grid with columns
                Debug.WriteLine("Initializing grid...");
                FormManagementHelper.InitializeGrid(this);
                Debug.WriteLine("Grid initialized successfully");

                // DEBUG: Test repository directly
                Debug.WriteLine("=== DEBUGGING: Testing repository directly ===");
                try
                {
                    var forms = FormManagementRepository.GetAllForms();
                    Debug.WriteLine($"Repository returned {forms?.Count ?? 0} forms:");
                    if (forms != null)
                    {
                        foreach (var form in forms)
                        {
                            Debug.WriteLine($"  - {form.FormId}: {form.FormName} ({form.DisplayName}) - {form.Category} - Active: {form.IsActive}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Repository test failed: {ex.Message}");
                    MessageBox.Show($"Repository test failed: {ex.Message}", "Debug Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

                // Load forms from database
                Debug.WriteLine("Loading forms from database...");
                FormManagementHelper.LoadFormsToGrid(this);
                Debug.WriteLine("Forms loaded successfully");

                // DEBUG: Check grid data after loading
                Debug.WriteLine("=== DEBUGGING: Checking grid data after loading ===");
                try
                {
                    var gridDataTable = this.GridDataTable as DataTable;
                    Debug.WriteLine($"Grid DataTable has {gridDataTable?.Rows.Count ?? 0} rows:");
                    if (gridDataTable != null)
                    {
                        foreach (DataRow row in gridDataTable.Rows)
                        {
                            Debug.WriteLine($"  - {row["form_id"]}: {row["form_name"]} ({row["display_name"]}) - {row["category"]} - Active: {row["is_active"]}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Grid data check failed: {ex.Message}");
                }

                // TEMPORARILY DISABLED: Setup event handlers
                Debug.WriteLine("Setting up event handlers...");
                //SetupEventHandlers();
                Debug.WriteLine("Event handlers setup completed (DISABLED)");

                Debug.WriteLine("FormManagement form loaded successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading FormManagement form: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error loading Form Management: {ex.Message}\n\nCheck debug output for details.", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Test database connection and provide diagnostics
        /// </summary>
        private void TestDatabaseConnection()
        {
            try
            {
                Debug.WriteLine("--- Testing Database Connection ---");

                var connectionManager = ProManage.Modules.Connections.DatabaseConnectionManager.Instance;
                Debug.WriteLine($"Connection Status: {connectionManager.IsConnected}");
                Debug.WriteLine($"Last Error: {connectionManager.LastError}");

                if (!connectionManager.IsConnected)
                {
                    Debug.WriteLine("Database not connected, attempting to connect...");
                    bool connected = connectionManager.OpenConnection();
                    Debug.WriteLine($"Connection attempt result: {connected}");

                    if (!connected)
                    {
                        Debug.WriteLine($"❌ Connection failed: {connectionManager.LastError}");
                        MessageBox.Show($"Database connection failed: {connectionManager.LastError}\n\nPlease check your database settings.",
                            "Database Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                }

                Debug.WriteLine("✅ Database connection successful");

                // Test if forms table exists
                TestFormsTable();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Database connection test failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Test if forms table exists and has data
        /// </summary>
        private void TestFormsTable()
        {
            try
            {
                using (var connection = ProManage.Modules.Connections.DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();

                    // Check if table exists
                    const string checkTableQuery = @"
                        SELECT EXISTS (
                            SELECT 1 FROM information_schema.tables
                            WHERE table_name = 'forms'
                        )";

                    using (var command = new Npgsql.NpgsqlCommand(checkTableQuery, connection))
                    {
                        bool tableExists = (bool)command.ExecuteScalar();
                        Debug.WriteLine($"Forms table exists: {tableExists}");

                        if (!tableExists)
                        {
                            Debug.WriteLine("❌ Forms table does not exist");
                            MessageBox.Show("Forms table does not exist in database.\n\nPlease run the Quick-Fix-FormManagement-Data.sql script.",
                                "Database Setup Required", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            return;
                        }
                    }

                    // Check row count
                    const string countQuery = "SELECT COUNT(*) FROM forms";
                    using (var command = new Npgsql.NpgsqlCommand(countQuery, connection))
                    {
                        int rowCount = Convert.ToInt32(command.ExecuteScalar());
                        Debug.WriteLine($"Forms table row count: {rowCount}");

                        if (rowCount == 0)
                        {
                            Debug.WriteLine("⚠️ Forms table is empty");
                            MessageBox.Show("Forms table exists but is empty.\n\nPlease run the Quick-Fix-FormManagement-Data.sql script to add sample data.",
                                "No Data", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            Debug.WriteLine($"✅ Forms table has {rowCount} records");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Forms table test failed: {ex.Message}");
            }
        }

        #endregion

        #region MenuRibbon Integration

        /// <summary>
        /// Initializes the MenuRibbon user control
        /// </summary>
        private void InitializeMenuRibbon()
        {
            try
            {
                // Skip in design mode
                if (DesignMode) return;

                menuRibbon = new MenuRibbon();
                menuRibbon.Dock = DockStyle.Top;

                // Set form name and user ID for permission checking
                menuRibbon.FormName = "FormManagement";
                menuRibbon.CurrentUserId = 1; // TODO: Get from current session

                // Configure ribbon for Form Management
                menuRibbon.ConfigureForFormType("FormManagement");

                // Add to form controls at the top
                this.Controls.Add(menuRibbon);
                menuRibbon.BringToFront();

                Debug.WriteLine("MenuRibbon initialized for FormManagement");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing MenuRibbon: {ex.Message}");
                // Continue without ribbon if there's an error
                menuRibbon = null;
            }
        }

        /// <summary>
        /// Sets up event handlers for ribbon buttons and grid events
        /// </summary>
        private void SetupEventHandlers()
        {
            try
            {
                if (menuRibbon != null)
                {
                    // New button event handler
                    menuRibbon.NewClicked += MenuRibbon_NewClicked;

                    // Edit button event handler
                    menuRibbon.EditClicked += MenuRibbon_EditClicked;

                    // Delete button event handler
                    menuRibbon.DeleteClicked += MenuRibbon_DeleteClicked;

                    // Add a temporary refresh button for testing
                    // Note: This uses the Print button as a temporary refresh button
                    menuRibbon.PrintClicked += MenuRibbon_RefreshClicked;
                    menuRibbon.SetButtonText("Print", "Refresh");
                }

                Debug.WriteLine("Event handlers setup completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up event handlers: {ex.Message}");
            }
        }

        #endregion

        #region Ribbon Event Handlers

        /// <summary>
        /// Handles New button click - creates a new form
        /// </summary>
        private void MenuRibbon_NewClicked(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("=== New button clicked ===");
                FormManagementHelper.CreateNewForm(this);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in New button click: {ex.Message}");
                MessageBox.Show($"Error creating new form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Edit button click - edits selected form
        /// </summary>
        private void MenuRibbon_EditClicked(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("=== Edit button clicked ===");
                
                var selectedForm = FormManagementHelper.GetSelectedForm(this);
                if (selectedForm == null)
                {
                    MessageBox.Show("Please select a form to edit.", "No Selection",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                FormManagementHelper.EditForm(this, selectedForm);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Edit button click: {ex.Message}");
                MessageBox.Show($"Error editing form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Refresh button click - reloads data from database
        /// </summary>
        private void MenuRibbon_RefreshClicked(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("=== Refresh button clicked ===");

                // Test database connection
                TestDatabaseConnection();

                // Reload data
                FormManagementHelper.LoadFormsToGrid(this);

                MessageBox.Show("Data refreshed successfully!", "Refresh Complete",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Refresh button click: {ex.Message}");
                MessageBox.Show($"Error refreshing data: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Delete button click - deactivates or deletes selected form
        /// </summary>
        private void MenuRibbon_DeleteClicked(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("=== Delete button clicked ===");
                
                var selectedForm = FormManagementHelper.GetSelectedForm(this);
                if (selectedForm == null)
                {
                    MessageBox.Show("Please select a form to delete.", "No Selection",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Show options for soft delete (deactivate) or hard delete
                var result = MessageBox.Show(
                    $"How would you like to remove '{selectedForm.FormName}'?\n\n" +
                    "Yes = Deactivate (recommended - preserves data)\n" +
                    "No = Permanent Delete (cannot be undone)\n" +
                    "Cancel = Do nothing",
                    "Delete Options",
                    MessageBoxButtons.YesNoCancel,
                    MessageBoxIcon.Question);

                switch (result)
                {
                    case DialogResult.Yes:
                        if (selectedForm.IsActive)
                        {
                            FormManagementHelper.DeactivateForm(this, selectedForm);
                        }
                        else
                        {
                            FormManagementHelper.ActivateForm(this, selectedForm);
                        }
                        break;
                    case DialogResult.No:
                        FormManagementHelper.DeleteForm(this, selectedForm);
                        break;
                    case DialogResult.Cancel:
                        // Do nothing
                        break;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Delete button click: {ex.Message}");
                MessageBox.Show($"Error deleting form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }



        #endregion

        #region Grid Event Handlers

        /// <summary>
        /// Handles grid selection changes to update button states
        /// </summary>
        private void GridView_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            try
            {
                FormManagementHelper.UpdateFormButtonStates(this);
                UpdateRibbonButtonStates();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in FocusedRowChanged: {ex.Message}");
            }
        }

        /// <summary>
        /// Updates ribbon button states based on selection
        /// </summary>
        private void UpdateRibbonButtonStates()
        {
            try
            {
                var selectedForm = FormManagementHelper.GetSelectedForm(this);
                bool hasSelection = selectedForm != null;

                if (menuRibbon != null)
                {
                    // Enable/disable buttons based on selection
                    menuRibbon.SetButtonEnabled("Edit", hasSelection);
                    menuRibbon.SetButtonEnabled("Delete", hasSelection);
                    
                    // Update delete button text based on form status
                    if (hasSelection)
                    {
                        string deleteText = selectedForm.IsActive ? "Deactivate" : "Activate";
                        menuRibbon.SetButtonText("Delete", deleteText);
                    }
                    else
                    {
                        menuRibbon.SetButtonText("Delete", "Delete");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating ribbon button states: {ex.Message}");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Refreshes the form data from database
        /// </summary>
        public void RefreshData()
        {
            FormManagementHelper.RefreshGrid(this);
        }

        /// <summary>
        /// Gets the currently selected form
        /// </summary>
        /// <returns>Selected form model or null</returns>
        public FormManagementModel GetSelectedForm()
        {
            return FormManagementHelper.GetSelectedForm(this);
        }

        #endregion
    }
}
