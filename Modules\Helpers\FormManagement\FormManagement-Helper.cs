// FormManagement Helper - Helper methods for Form Management operations
// Usage: Provides utility methods for grid management, data binding, and form operations

using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraEditors;
using ProManage.Modules.Data.FormManagement;
using ProManage.Modules.Models.FormManagement;
using ProManage.Modules.Services;
using ProManage.Modules.UI;
using ProManage.Forms.Dialogs;

namespace ProManage.Modules.Helpers.FormManagement
{
    /// <summary>
    /// Helper class for Form Management UI operations
    /// Provides grid management, data binding, and form utility methods
    /// </summary>
    public static class FormManagementHelper
    {
        #region Grid Initialization

        /// <summary>
        /// Initializes the grid with proper columns and formatting
        /// </summary>
        /// <param name="form">The Form Management form instance</param>
        public static void InitializeGrid(dynamic form)
        {
            try
            {
                Debug.WriteLine("=== FormManagementHelper.InitializeGrid: Starting ===");

                // Get the grid control from the form
                GridControl gridControl = form.gridControl1 as GridControl;
                GridView gridView = gridControl?.MainView as GridView;

                if (gridControl == null || gridView == null)
                {
                    throw new Exception("Grid control or grid view not found on form");
                }

                // Create DataTable for grid binding
                var gridDataTable = CreateGridDataTable();
                form.GridDataTable = gridDataTable;

                // Bind the grid to the DataTable
                gridControl.DataSource = gridDataTable;

                // Configure grid appearance and behavior
                ConfigureGridAppearance(gridView);
                ConfigureGridColumns(gridView);
                SetupGridEventHandlers(gridView, form);

                Debug.WriteLine("Grid initialization completed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in InitializeGrid: {ex.Message}");
                throw new Exception($"Failed to initialize grid: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Creates the DataTable structure for grid binding
        /// </summary>
        /// <returns>Configured DataTable</returns>
        private static DataTable CreateGridDataTable()
        {
            var dataTable = new DataTable("FormManagement");

            // Add columns matching the database structure
            dataTable.Columns.Add("form_id", typeof(int));
            dataTable.Columns.Add("form_name", typeof(string));
            dataTable.Columns.Add("display_name", typeof(string));
            dataTable.Columns.Add("category", typeof(string));
            dataTable.Columns.Add("is_active", typeof(bool));
            dataTable.Columns.Add("last_modified", typeof(DateTime));

            // Set primary key
            dataTable.PrimaryKey = new DataColumn[] { dataTable.Columns["form_id"] };

            return dataTable;
        }

        /// <summary>
        /// Configures grid appearance and behavior
        /// </summary>
        /// <param name="gridView">Grid view to configure</param>
        private static void ConfigureGridAppearance(GridView gridView)
        {
            // Basic grid settings
            gridView.OptionsView.ShowGroupPanel = false;
            gridView.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never;
            gridView.OptionsView.ShowAutoFilterRow = true;
            gridView.OptionsSelection.MultiSelect = false;
            gridView.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.RowSelect;

            // Row appearance
            gridView.OptionsView.ShowIndicator = true;
            gridView.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.True;
            gridView.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.True;

            // Editing settings
            gridView.OptionsBehavior.Editable = false;
            gridView.OptionsBehavior.ReadOnly = true;

            // Appearance settings following ProManage standards
            gridView.Appearance.Row.BackColor = Color.FromArgb(245, 245, 245);
            gridView.Appearance.EvenRow.BackColor = Color.White;
            gridView.Appearance.SelectedRow.BackColor = Color.FromArgb(51, 153, 255);
            gridView.Appearance.SelectedRow.ForeColor = Color.White;

            // Font settings
            gridView.Appearance.Row.Font = new Font("Segoe UI", 9F);
            gridView.Appearance.HeaderPanel.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
        }

        /// <summary>
        /// Configures grid columns with proper formatting and headers
        /// </summary>
        /// <param name="gridView">Grid view to configure</param>
        private static void ConfigureGridColumns(GridView gridView)
        {
            // Clear existing columns
            gridView.Columns.Clear();

            // Form ID column (hidden)
            var colFormId = gridView.Columns.AddField("form_id");
            colFormId.Caption = "ID";
            colFormId.Visible = false;
            colFormId.OptionsColumn.AllowEdit = false;

            // Form Name column
            var colFormName = gridView.Columns.AddField("form_name");
            colFormName.Caption = "Form Name";
            colFormName.Width = 200;
            colFormName.OptionsColumn.AllowEdit = false;
            colFormName.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;

            // Display Name column
            var colDisplayName = gridView.Columns.AddField("display_name");
            colDisplayName.Caption = "Display Name";
            colDisplayName.Width = 250;
            colDisplayName.OptionsColumn.AllowEdit = false;
            colDisplayName.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;

            // Category column
            var colCategory = gridView.Columns.AddField("category");
            colCategory.Caption = "Category";
            colCategory.Width = 150;
            colCategory.OptionsColumn.AllowEdit = false;
            colCategory.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;

            // Active Status column
            var colIsActive = gridView.Columns.AddField("is_active");
            colIsActive.Caption = "Active";
            colIsActive.Width = 80;
            colIsActive.OptionsColumn.AllowEdit = false;
            colIsActive.ColumnEdit = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();

            // Last Modified column
            var colLastModified = gridView.Columns.AddField("last_modified");
            colLastModified.Caption = "Last Modified";
            colLastModified.Width = 150;
            colLastModified.OptionsColumn.AllowEdit = false;
            colLastModified.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            colLastModified.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";

            // Set column order
            colFormName.VisibleIndex = 0;
            colDisplayName.VisibleIndex = 1;
            colCategory.VisibleIndex = 2;
            colIsActive.VisibleIndex = 3;
            colLastModified.VisibleIndex = 4;
        }

        /// <summary>
        /// Sets up event handlers for grid operations
        /// </summary>
        /// <param name="gridView">Grid view to configure</param>
        /// <param name="form">Parent form instance</param>
        private static void SetupGridEventHandlers(GridView gridView, dynamic form)
        {
            // Row selection changed event
            gridView.FocusedRowChanged += (sender, e) =>
            {
                try
                {
                    UpdateFormButtonStates(form);
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error in FocusedRowChanged: {ex.Message}");
                }
            };

            // Double-click to edit
            gridView.DoubleClick += (sender, e) =>
            {
                try
                {
                    var selectedForm = GetSelectedForm(form);
                    if (selectedForm != null)
                    {
                        EditForm(form, selectedForm);
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error in DoubleClick: {ex.Message}");
                    MessageBox.Show($"Error opening form for editing: {ex.Message}", "Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };
        }

        #endregion

        #region Data Loading

        /// <summary>
        /// Loads all forms from database and displays them in the grid
        /// </summary>
        /// <param name="form">The Form Management form instance</param>
        public static void LoadFormsToGrid(dynamic form)
        {
            // Show progress indicator for database operation
            ProgressIndicatorService.Instance.ShowProgress();

            try
            {
                Debug.WriteLine("=== FormManagementHelper.LoadFormsToGrid: Starting ===");

                // Clear existing data
                var gridDataTable = form.GridDataTable as DataTable;
                gridDataTable.Clear();

                // Get forms from repository
                var forms = FormManagementRepository.GetAllForms();
                Debug.WriteLine($"Retrieved {forms.Count} forms from repository");

                // Populate the grid
                foreach (var formModel in forms)
                {
                    var row = gridDataTable.NewRow();
                    row["form_id"] = formModel.FormId;
                    row["form_name"] = formModel.FormName;
                    row["display_name"] = formModel.DisplayName ?? "";
                    row["category"] = formModel.Category ?? "";
                    row["is_active"] = formModel.IsActive;
                    row["last_modified"] = formModel.LastModified;
                    gridDataTable.Rows.Add(row);
                }

                // Update button states
                UpdateFormButtonStates(form);

                Debug.WriteLine($"Grid populated with {forms.Count} forms");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in LoadFormsToGrid: {ex.Message}");
                MessageBox.Show($"Error loading forms: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Always hide progress indicator
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        /// <summary>
        /// Refreshes the grid data from database
        /// </summary>
        /// <param name="form">The Form Management form instance</param>
        public static void RefreshGrid(dynamic form)
        {
            LoadFormsToGrid(form);
        }

        #endregion

        #region Form Operations

        /// <summary>
        /// Gets the currently selected form from the grid
        /// </summary>
        /// <param name="form">The Form Management form instance</param>
        /// <returns>Selected FormManagementModel or null</returns>
        public static FormManagementModel GetSelectedForm(dynamic form)
        {
            try
            {
                GridControl gridControl = form.gridControl1 as GridControl;
                GridView gridView = gridControl?.MainView as GridView;

                if (gridView == null || gridView.FocusedRowHandle < 0)
                    return null;

                var row = gridView.GetDataRow(gridView.FocusedRowHandle);
                if (row == null) return null;

                return FormManagementModel.FromDataRow(row);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetSelectedForm: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Updates the state of form buttons based on selection
        /// </summary>
        /// <param name="form">The Form Management form instance</param>
        public static void UpdateFormButtonStates(dynamic form)
        {
            try
            {
                var selectedForm = GetSelectedForm(form);
                bool hasSelection = selectedForm != null;

                // Update button states through MenuRibbon if available
                // This will be implemented when we create the main form
                Debug.WriteLine($"Button states updated - Has selection: {hasSelection}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in UpdateFormButtonStates: {ex.Message}");
            }
        }

        #endregion

        #region CRUD Operations

        /// <summary>
        /// Opens the form entry dialog for creating a new form
        /// </summary>
        /// <param name="parentForm">Parent form instance</param>
        public static void CreateNewForm(dynamic parentForm)
        {
            try
            {
                Debug.WriteLine("=== FormManagementHelper.CreateNewForm: Starting ===");

                using (var entryForm = new FormManagementEntryForm())
                {
                    entryForm.Text = "Create New Form";

                    var result = entryForm.ShowDialog();
                    if (result == DialogResult.OK)
                    {
                        // Refresh the grid to show the new form
                        RefreshGrid(parentForm);
                        MessageBox.Show("Form created successfully!", "Success",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in CreateNewForm: {ex.Message}");
                MessageBox.Show($"Error creating form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Opens the form entry dialog for editing an existing form
        /// </summary>
        /// <param name="parentForm">Parent form instance</param>
        /// <param name="formToEdit">Form model to edit</param>
        public static void EditForm(dynamic parentForm, FormManagementModel formToEdit)
        {
            try
            {
                Debug.WriteLine($"=== FormManagementHelper.EditForm: {formToEdit.FormId} ===");

                using (var entryForm = new FormManagementEntryForm(formToEdit))
                {
                    entryForm.Text = $"Edit Form - {formToEdit.FormName}";

                    var result = entryForm.ShowDialog();
                    if (result == DialogResult.OK)
                    {
                        // Refresh the grid to show the updated form
                        RefreshGrid(parentForm);
                        MessageBox.Show("Form updated successfully!", "Success",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in EditForm: {ex.Message}");
                MessageBox.Show($"Error editing form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Deactivates the selected form (soft delete)
        /// </summary>
        /// <param name="parentForm">Parent form instance</param>
        /// <param name="formToDeactivate">Form model to deactivate</param>
        public static void DeactivateForm(dynamic parentForm, FormManagementModel formToDeactivate)
        {
            try
            {
                Debug.WriteLine($"=== FormManagementHelper.DeactivateForm: {formToDeactivate.FormId} ===");

                var confirmMessage = $"Are you sure you want to deactivate the form '{formToDeactivate.FormName}'?\n\n" +
                                   "This will make the form inactive but preserve all data.";

                var result = MessageBox.Show(confirmMessage, "Confirm Deactivation",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    ProgressIndicatorService.Instance.ShowProgress();

                    try
                    {
                        FormManagementRepository.DeactivateForm(formToDeactivate.FormId);
                        RefreshGrid(parentForm);
                        MessageBox.Show("Form deactivated successfully!", "Success",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    finally
                    {
                        ProgressIndicatorService.Instance.HideProgress();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in DeactivateForm: {ex.Message}");
                MessageBox.Show($"Error deactivating form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Activates the selected form
        /// </summary>
        /// <param name="parentForm">Parent form instance</param>
        /// <param name="formToActivate">Form model to activate</param>
        public static void ActivateForm(dynamic parentForm, FormManagementModel formToActivate)
        {
            try
            {
                Debug.WriteLine($"=== FormManagementHelper.ActivateForm: {formToActivate.FormId} ===");

                ProgressIndicatorService.Instance.ShowProgress();

                try
                {
                    FormManagementRepository.ActivateForm(formToActivate.FormId);
                    RefreshGrid(parentForm);
                    MessageBox.Show("Form activated successfully!", "Success",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                finally
                {
                    ProgressIndicatorService.Instance.HideProgress();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ActivateForm: {ex.Message}");
                MessageBox.Show($"Error activating form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Permanently deletes the selected form (hard delete)
        /// </summary>
        /// <param name="parentForm">Parent form instance</param>
        /// <param name="formToDelete">Form model to delete</param>
        public static void DeleteForm(dynamic parentForm, FormManagementModel formToDelete)
        {
            try
            {
                Debug.WriteLine($"=== FormManagementHelper.DeleteForm: {formToDelete.FormId} ===");

                var confirmMessage = $"Are you sure you want to PERMANENTLY DELETE the form '{formToDelete.FormName}'?\n\n" +
                                   "WARNING: This action cannot be undone!\n" +
                                   "All related permission data will also be removed.\n\n" +
                                   "Type 'DELETE' to confirm:";

                // Simple confirmation dialog for permanent deletion
                var confirmResult = MessageBox.Show(confirmMessage, "Confirm Permanent Deletion",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                if (confirmResult == DialogResult.Yes)
                {
                    ProgressIndicatorService.Instance.ShowProgress();

                    try
                    {
                        FormManagementRepository.DeleteForm(formToDelete.FormId);
                        RefreshGrid(parentForm);
                        MessageBox.Show("Form deleted permanently!", "Success",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    finally
                    {
                        ProgressIndicatorService.Instance.HideProgress();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in DeleteForm: {ex.Message}");
                MessageBox.Show($"Error deleting form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Populates a ComboBox with available categories
        /// </summary>
        /// <param name="comboBox">ComboBox to populate</param>
        public static void PopulateCategoryComboBox(ComboBoxEdit comboBox)
        {
            try
            {
                Debug.WriteLine("=== FormManagementHelper.PopulateCategoryComboBox: Starting ===");

                var categories = FormManagementRepository.GetCategories();

                comboBox.Properties.Items.Clear();
                comboBox.Properties.Items.Add(""); // Empty option

                foreach (var category in categories)
                {
                    comboBox.Properties.Items.Add(category);
                }

                Debug.WriteLine($"Category ComboBox populated with {categories.Count} categories");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in PopulateCategoryComboBox: {ex.Message}");
            }
        }

        /// <summary>
        /// Validates form name availability
        /// </summary>
        /// <param name="formName">Form name to validate</param>
        /// <param name="excludeFormId">Form ID to exclude from check</param>
        /// <returns>True if available</returns>
        public static bool ValidateFormNameAvailability(string formName, int? excludeFormId = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(formName))
                    return false;

                return FormManagementRepository.IsFormNameAvailable(formName, excludeFormId);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ValidateFormNameAvailability: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
