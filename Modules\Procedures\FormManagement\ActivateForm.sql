-- Activate Form
-- Reactivates a deactivated form
DO $$
DECLARE
    existing_count INTEGER;
    updated_count INTEGER;
BEGIN
    -- Set transaction isolation level for consistency
    SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
    
    -- Validate form ID
    IF @form_id IS NULL OR @form_id <= 0 THEN
        RAISE EXCEPTION 'Valid form ID is required';
    END IF;
    
    -- Check if form exists
    SELECT COUNT(*) INTO existing_count
    FROM forms 
    WHERE form_id = @form_id;
    
    IF existing_count = 0 THEN
        RAISE EXCEPTION 'Form with ID % does not exist', @form_id;
    END IF;
    
    -- Check if form is already active
    SELECT COUNT(*) INTO existing_count
    FROM forms 
    WHERE form_id = @form_id AND is_active = true;
    
    IF existing_count > 0 THEN
        RAISE EXCEPTION 'Form is already active';
    END IF;
    
    -- Activate the form
    UPDATE forms SET
        is_active = true,
        last_modified = CURRENT_TIMESTAMP
    WHERE form_id = @form_id;
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    -- Return success message
    SELECT 
        @form_id as form_id,
        'Form activated successfully' as message;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error activating form: %', SQLERRM;
END $$;
