-- FormManagement CRUD Operations
-- Comprehensive SQL procedures for Form Management operations
-- Includes proper error handling, transactions, and validation

-- =====================================================
-- GET ALL FORMS
-- =====================================================
-- GetAllForms
SELECT 
    form_id,
    form_name,
    display_name,
    category,
    is_active,
    last_modified
FROM forms 
ORDER BY 
    CASE WHEN is_active THEN 0 ELSE 1 END,  -- Active forms first
    category NULLS LAST,                     -- Group by category
    display_name NULLS LAST,                 -- Then by display name
    form_name;                               -- Finally by form name

-- =====================================================
-- GET FORM BY ID
-- =====================================================
-- GetFormById
SELECT 
    form_id,
    form_name,
    display_name,
    category,
    is_active,
    last_modified
FROM forms 
WHERE form_id = @form_id;

-- =====================================================
-- GET ACTIVE FORMS ONLY
-- =====================================================
-- GetActiveForms
SELECT 
    form_id,
    form_name,
    display_name,
    category,
    is_active,
    last_modified
FROM forms 
WHERE is_active = true
ORDER BY 
    category NULLS LAST,
    display_name NULLS LAST,
    form_name;

-- =====================================================
-- GET FORMS BY CATEGORY
-- =====================================================
-- GetFormsByCategory
SELECT 
    form_id,
    form_name,
    display_name,
    category,
    is_active,
    last_modified
FROM forms 
WHERE category = @category
ORDER BY 
    CASE WHEN is_active THEN 0 ELSE 1 END,
    display_name NULLS LAST,
    form_name;

-- =====================================================
-- INSERT NEW FORM
-- =====================================================
-- InsertForm
DO $$
DECLARE
    new_form_id INTEGER;
    existing_count INTEGER;
BEGIN
    -- Set transaction isolation level for consistency
    SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
    
    -- Validate required fields
    IF @form_name IS NULL OR TRIM(@form_name) = '' THEN
        RAISE EXCEPTION 'Form name is required and cannot be empty';
    END IF;
    
    -- Check for duplicate form_name (case-insensitive)
    SELECT COUNT(*) INTO existing_count
    FROM forms 
    WHERE LOWER(TRIM(form_name)) = LOWER(TRIM(@form_name));
    
    IF existing_count > 0 THEN
        RAISE EXCEPTION 'A form with the name "%" already exists', @form_name;
    END IF;
    
    -- Validate form_name format (alphanumeric and underscores only)
    IF @form_name !~ '^[A-Za-z][A-Za-z0-9_]*$' THEN
        RAISE EXCEPTION 'Form name must start with a letter and contain only letters, numbers, and underscores';
    END IF;
    
    -- Insert the new form
    INSERT INTO forms (
        form_name,
        display_name,
        category,
        is_active,
        last_modified
    ) VALUES (
        TRIM(@form_name),
        CASE WHEN @display_name IS NULL OR TRIM(@display_name) = '' 
             THEN NULL 
             ELSE TRIM(@display_name) 
        END,
        CASE WHEN @category IS NULL OR TRIM(@category) = '' 
             THEN NULL 
             ELSE TRIM(@category) 
        END,
        COALESCE(@is_active, true),
        CURRENT_TIMESTAMP
    ) RETURNING form_id INTO new_form_id;
    
    -- Return the new form data
    SELECT 
        form_id,
        form_name,
        display_name,
        category,
        is_active,
        last_modified,
        'Form created successfully' as message
    FROM forms 
    WHERE form_id = new_form_id;
    
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error and re-raise with user-friendly message
        RAISE EXCEPTION 'Error creating form: %', SQLERRM;
END $$;

-- =====================================================
-- UPDATE EXISTING FORM
-- =====================================================
-- UpdateForm
DO $$
DECLARE
    existing_count INTEGER;
    updated_count INTEGER;
BEGIN
    -- Set transaction isolation level for consistency
    SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
    
    -- Validate required fields
    IF @form_id IS NULL OR @form_id <= 0 THEN
        RAISE EXCEPTION 'Valid form ID is required';
    END IF;
    
    IF @form_name IS NULL OR TRIM(@form_name) = '' THEN
        RAISE EXCEPTION 'Form name is required and cannot be empty';
    END IF;
    
    -- Check if form exists
    SELECT COUNT(*) INTO existing_count
    FROM forms 
    WHERE form_id = @form_id;
    
    IF existing_count = 0 THEN
        RAISE EXCEPTION 'Form with ID % does not exist', @form_id;
    END IF;
    
    -- Check for duplicate form_name (excluding current form)
    SELECT COUNT(*) INTO existing_count
    FROM forms 
    WHERE LOWER(TRIM(form_name)) = LOWER(TRIM(@form_name))
      AND form_id != @form_id;
    
    IF existing_count > 0 THEN
        RAISE EXCEPTION 'A form with the name "%" already exists', @form_name;
    END IF;
    
    -- Validate form_name format
    IF @form_name !~ '^[A-Za-z][A-Za-z0-9_]*$' THEN
        RAISE EXCEPTION 'Form name must start with a letter and contain only letters, numbers, and underscores';
    END IF;
    
    -- Update the form
    UPDATE forms SET
        form_name = TRIM(@form_name),
        display_name = CASE WHEN @display_name IS NULL OR TRIM(@display_name) = '' 
                           THEN NULL 
                           ELSE TRIM(@display_name) 
                      END,
        category = CASE WHEN @category IS NULL OR TRIM(@category) = '' 
                       THEN NULL 
                       ELSE TRIM(@category) 
                  END,
        is_active = COALESCE(@is_active, is_active),
        last_modified = CURRENT_TIMESTAMP
    WHERE form_id = @form_id;
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    IF updated_count = 0 THEN
        RAISE EXCEPTION 'No changes were made to the form';
    END IF;
    
    -- Return the updated form data
    SELECT 
        form_id,
        form_name,
        display_name,
        category,
        is_active,
        last_modified,
        'Form updated successfully' as message
    FROM forms 
    WHERE form_id = @form_id;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error updating form: %', SQLERRM;
END $$;

-- =====================================================
-- SOFT DELETE (DEACTIVATE) FORM
-- =====================================================
-- DeactivateForm
DO $$
DECLARE
    existing_count INTEGER;
    updated_count INTEGER;
BEGIN
    -- Set transaction isolation level for consistency
    SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
    
    -- Validate form ID
    IF @form_id IS NULL OR @form_id <= 0 THEN
        RAISE EXCEPTION 'Valid form ID is required';
    END IF;
    
    -- Check if form exists
    SELECT COUNT(*) INTO existing_count
    FROM forms 
    WHERE form_id = @form_id;
    
    IF existing_count = 0 THEN
        RAISE EXCEPTION 'Form with ID % does not exist', @form_id;
    END IF;
    
    -- Check if form is already inactive
    SELECT COUNT(*) INTO existing_count
    FROM forms 
    WHERE form_id = @form_id AND is_active = false;
    
    IF existing_count > 0 THEN
        RAISE EXCEPTION 'Form is already deactivated';
    END IF;
    
    -- Deactivate the form
    UPDATE forms SET
        is_active = false,
        last_modified = CURRENT_TIMESTAMP
    WHERE form_id = @form_id;
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    -- Return success message
    SELECT 
        @form_id as form_id,
        'Form deactivated successfully' as message;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error deactivating form: %', SQLERRM;
END $$;

-- =====================================================
-- HARD DELETE FORM
-- =====================================================
-- DeleteForm
DO $$
DECLARE
    existing_count INTEGER;
    deleted_count INTEGER;
    form_name_val VARCHAR(255);
BEGIN
    -- Set transaction isolation level for consistency
    SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
    
    -- Validate form ID
    IF @form_id IS NULL OR @form_id <= 0 THEN
        RAISE EXCEPTION 'Valid form ID is required';
    END IF;
    
    -- Check if form exists and get form name for confirmation
    SELECT COUNT(*), MAX(form_name) INTO existing_count, form_name_val
    FROM forms 
    WHERE form_id = @form_id;
    
    IF existing_count = 0 THEN
        RAISE EXCEPTION 'Form with ID % does not exist', @form_id;
    END IF;
    
    -- Warning: This will trigger the permission sync function
    -- which will clean up related permission records
    DELETE FROM forms 
    WHERE form_id = @form_id;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Return success message
    SELECT 
        @form_id as form_id,
        form_name_val as form_name,
        'Form deleted successfully. Related permissions have been cleaned up.' as message;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error deleting form: %', SQLERRM;
END $$;

-- =====================================================
-- ACTIVATE FORM
-- =====================================================
-- ActivateForm
DO $$
DECLARE
    existing_count INTEGER;
    updated_count INTEGER;
BEGIN
    -- Set transaction isolation level for consistency
    SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
    
    -- Validate form ID
    IF @form_id IS NULL OR @form_id <= 0 THEN
        RAISE EXCEPTION 'Valid form ID is required';
    END IF;
    
    -- Check if form exists
    SELECT COUNT(*) INTO existing_count
    FROM forms 
    WHERE form_id = @form_id;
    
    IF existing_count = 0 THEN
        RAISE EXCEPTION 'Form with ID % does not exist', @form_id;
    END IF;
    
    -- Check if form is already active
    SELECT COUNT(*) INTO existing_count
    FROM forms 
    WHERE form_id = @form_id AND is_active = true;
    
    IF existing_count > 0 THEN
        RAISE EXCEPTION 'Form is already active';
    END IF;
    
    -- Activate the form
    UPDATE forms SET
        is_active = true,
        last_modified = CURRENT_TIMESTAMP
    WHERE form_id = @form_id;
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    -- Return success message
    SELECT 
        @form_id as form_id,
        'Form activated successfully' as message;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error activating form: %', SQLERRM;
END $$;

-- =====================================================
-- GET DISTINCT CATEGORIES
-- =====================================================
-- GetCategories
SELECT DISTINCT 
    category
FROM forms 
WHERE category IS NOT NULL 
  AND TRIM(category) != ''
ORDER BY category;

-- =====================================================
-- VALIDATE FORM NAME UNIQUENESS
-- =====================================================
-- ValidateFormName
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'EXISTS'
        ELSE 'AVAILABLE'
    END as status,
    COUNT(*) as count
FROM forms 
WHERE LOWER(TRIM(form_name)) = LOWER(TRIM(@form_name))
  AND (@exclude_form_id IS NULL OR form_id != @exclude_form_id);