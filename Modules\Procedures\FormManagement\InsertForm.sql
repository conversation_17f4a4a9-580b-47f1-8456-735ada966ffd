-- Insert New Form
-- Creates a new form with validation
DO $$
DECLARE
    new_form_id INTEGER;
    existing_count INTEGER;
BEGIN
    -- Set transaction isolation level for consistency
    SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
    
    -- Validate required fields
    IF @form_name IS NULL OR TRIM(@form_name) = '' THEN
        RAISE EXCEPTION 'Form name is required and cannot be empty';
    END IF;
    
    -- Check for duplicate form_name (case-insensitive)
    SELECT COUNT(*) INTO existing_count
    FROM forms 
    WHERE LOWER(TRIM(form_name)) = LOWER(TRIM(@form_name));
    
    IF existing_count > 0 THEN
        RAISE EXCEPTION 'A form with the name "%" already exists', @form_name;
    END IF;
    
    -- Validate form_name format (alphanumeric and underscores only)
    IF @form_name !~ '^[A-Za-z][A-Za-z0-9_]*$' THEN
        RAISE EXCEPTION 'Form name must start with a letter and contain only letters, numbers, and underscores';
    END IF;
    
    -- Insert the new form
    INSERT INTO forms (
        form_name,
        display_name,
        category,
        is_active,
        last_modified
    ) VALUES (
        TRIM(@form_name),
        CASE WHEN @display_name IS NULL OR TRIM(@display_name) = '' 
             THEN NULL 
             ELSE TRIM(@display_name) 
        END,
        CASE WHEN @category IS NULL OR TRIM(@category) = '' 
             THEN NULL 
             ELSE TRIM(@category) 
        END,
        COALESCE(@is_active, true),
        CURRENT_TIMESTAMP
    ) RETURNING form_id INTO new_form_id;
    
    -- Return the new form data
    SELECT 
        form_id,
        form_name,
        display_name,
        category,
        is_active,
        last_modified,
        'Form created successfully' as message
    FROM forms 
    WHERE form_id = new_form_id;
    
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error and re-raise with user-friendly message
        RAISE EXCEPTION 'Error creating form: %', SQLERRM;
END $$;
