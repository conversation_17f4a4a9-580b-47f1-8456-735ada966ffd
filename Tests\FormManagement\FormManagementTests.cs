// FormManagement Tests - Unit tests for Form Management functionality
// Usage: Run with dotnet test command to validate Form Management operations

using System;
using System.Diagnostics;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ProManage.Modules.Data.FormManagement;
using ProManage.Modules.Models.FormManagement;
using ProManage.Modules.Validation.FormManagement;

namespace ProManage.Tests.FormManagement
{
    /// <summary>
    /// Test class for Form Management functionality
    /// Tests CRUD operations, validation, and business rules
    /// </summary>
    [TestClass]
    public class FormManagementTests
    {
        #region Test Setup

        [TestInitialize]
        public void TestInitialize()
        {
            Debug.WriteLine("=== FormManagement Test Initialize ===");
        }

        [TestCleanup]
        public void TestCleanup()
        {
            Debug.WriteLine("=== FormManagement Test Cleanup ===");
        }

        #endregion

        #region Model Tests

        [TestMethod]
        public void FormManagementModel_Constructor_SetsDefaultValues()
        {
            // Arrange & Act
            var form = new FormManagementModel();

            // Assert
            Assert.IsTrue(form.IsActive, "New forms should be active by default");
            Assert.AreEqual(0, form.FormId, "New forms should have FormId = 0");
            Assert.IsTrue(form.LastModified > DateTime.MinValue, "LastModified should be set");
        }

        [TestMethod]
        public void FormManagementModel_ConstructorWithParameters_SetsValues()
        {
            // Arrange
            string formName = "TestForm";
            string displayName = "Test Form";
            string category = "Testing";

            // Act
            var form = new FormManagementModel(formName, displayName, category);

            // Assert
            Assert.AreEqual(formName, form.FormName);
            Assert.AreEqual(displayName, form.DisplayName);
            Assert.AreEqual(category, form.Category);
            Assert.IsTrue(form.IsActive);
        }

        [TestMethod]
        public void FormManagementModel_IsFormNameValid_ValidatesCorrectly()
        {
            // Arrange & Act & Assert
            var validForm = new FormManagementModel("ValidFormName");
            Assert.IsTrue(validForm.IsFormNameValid(), "Valid form name should pass validation");

            var invalidForm1 = new FormManagementModel("123InvalidName");
            Assert.IsFalse(invalidForm1.IsFormNameValid(), "Form name starting with number should fail");

            var invalidForm2 = new FormManagementModel("Invalid-Name");
            Assert.IsFalse(invalidForm2.IsFormNameValid(), "Form name with hyphen should fail");

            var invalidForm3 = new FormManagementModel("");
            Assert.IsFalse(invalidForm3.IsFormNameValid(), "Empty form name should fail");
        }

        [TestMethod]
        public void FormManagementModel_Normalize_CleansData()
        {
            // Arrange
            var form = new FormManagementModel("  TestForm  ", "  Test Form  ", "  Testing  ");

            // Act
            form.Normalize();

            // Assert
            Assert.AreEqual("TestForm", form.FormName);
            Assert.AreEqual("Test Form", form.DisplayName);
            Assert.AreEqual("Testing", form.Category);
        }

        [TestMethod]
        public void FormManagementModel_Clone_CreatesExactCopy()
        {
            // Arrange
            var original = new FormManagementModel(1, "TestForm", "Test Form", "Testing", true, DateTime.Now);

            // Act
            var clone = original.Clone();

            // Assert
            Assert.AreEqual(original.FormId, clone.FormId);
            Assert.AreEqual(original.FormName, clone.FormName);
            Assert.AreEqual(original.DisplayName, clone.DisplayName);
            Assert.AreEqual(original.Category, clone.Category);
            Assert.AreEqual(original.IsActive, clone.IsActive);
            Assert.AreEqual(original.LastModified, clone.LastModified);
        }

        #endregion

        #region Validation Tests

        [TestMethod]
        public void FormManagementValidation_ValidateFormName_RequiredField()
        {
            // Arrange & Act
            var result1 = FormManagementValidation.ValidateFormName(null);
            var result2 = FormManagementValidation.ValidateFormName("");
            var result3 = FormManagementValidation.ValidateFormName("   ");

            // Assert
            Assert.IsFalse(result1.IsValid, "Null form name should fail validation");
            Assert.IsFalse(result2.IsValid, "Empty form name should fail validation");
            Assert.IsFalse(result3.IsValid, "Whitespace-only form name should fail validation");
        }

        [TestMethod]
        public void FormManagementValidation_ValidateFormName_FormatValidation()
        {
            // Arrange & Act
            var validResult = FormManagementValidation.ValidateFormName("ValidFormName");
            var invalidResult1 = FormManagementValidation.ValidateFormName("123InvalidName");
            var invalidResult2 = FormManagementValidation.ValidateFormName("Invalid-Name");
            var invalidResult3 = FormManagementValidation.ValidateFormName("Invalid Name");

            // Assert
            Assert.IsTrue(validResult.IsValid, "Valid form name should pass");
            Assert.IsFalse(invalidResult1.IsValid, "Form name starting with number should fail");
            Assert.IsFalse(invalidResult2.IsValid, "Form name with hyphen should fail");
            Assert.IsFalse(invalidResult3.IsValid, "Form name with space should fail");
        }

        [TestMethod]
        public void FormManagementValidation_ValidateDisplayName_OptionalField()
        {
            // Arrange & Act
            var result1 = FormManagementValidation.ValidateDisplayName(null);
            var result2 = FormManagementValidation.ValidateDisplayName("");
            var result3 = FormManagementValidation.ValidateDisplayName("Valid Display Name");

            // Assert
            Assert.IsTrue(result1.IsValid, "Null display name should be valid (optional)");
            Assert.IsTrue(result2.IsValid, "Empty display name should be valid (optional)");
            Assert.IsTrue(result3.IsValid, "Valid display name should pass");
        }

        [TestMethod]
        public void FormManagementValidation_ValidateCategory_OptionalField()
        {
            // Arrange & Act
            var result1 = FormManagementValidation.ValidateCategory(null);
            var result2 = FormManagementValidation.ValidateCategory("");
            var result3 = FormManagementValidation.ValidateCategory("Administration");

            // Assert
            Assert.IsTrue(result1.IsValid, "Null category should be valid (optional)");
            Assert.IsTrue(result2.IsValid, "Empty category should be valid (optional)");
            Assert.IsTrue(result3.IsValid, "Valid category should pass");
        }

        [TestMethod]
        public void FormManagementValidation_ValidateForm_CompleteValidation()
        {
            // Arrange
            var validForm = new FormManagementModel("ValidFormName", "Valid Display Name", "Administration");
            var invalidForm = new FormManagementModel("123Invalid", null, null);

            // Act
            var validResult = FormManagementValidation.ValidateForm(validForm);
            var invalidResult = FormManagementValidation.ValidateForm(invalidForm);

            // Assert
            Assert.IsTrue(validResult.IsValid, "Valid form should pass complete validation");
            Assert.IsFalse(invalidResult.IsValid, "Invalid form should fail complete validation");
        }

        [TestMethod]
        public void FormManagementValidation_IsValidFormNameFormat_QuickValidation()
        {
            // Arrange & Act & Assert
            Assert.IsTrue(FormManagementValidation.IsValidFormNameFormat("ValidName"));
            Assert.IsTrue(FormManagementValidation.IsValidFormNameFormat("Valid_Name_123"));
            Assert.IsFalse(FormManagementValidation.IsValidFormNameFormat("123Invalid"));
            Assert.IsFalse(FormManagementValidation.IsValidFormNameFormat("Invalid-Name"));
            Assert.IsFalse(FormManagementValidation.IsValidFormNameFormat(""));
            Assert.IsFalse(FormManagementValidation.IsValidFormNameFormat(null));
        }

        [TestMethod]
        public void FormManagementValidation_SuggestFormName_GeneratesValidSuggestion()
        {
            // Arrange & Act
            var suggestion1 = FormManagementValidation.SuggestFormName("User Management");
            var suggestion2 = FormManagementValidation.SuggestFormName("Role & Permission Setup");
            var suggestion3 = FormManagementValidation.SuggestFormName("123 Test Form");

            // Assert
            Assert.AreEqual("UserManagement", suggestion1);
            Assert.AreEqual("RolePermissionSetup", suggestion2);
            Assert.AreEqual("FormTestForm", suggestion3);
            
            // Verify suggestions are valid form names
            Assert.IsTrue(FormManagementValidation.IsValidFormNameFormat(suggestion1));
            Assert.IsTrue(FormManagementValidation.IsValidFormNameFormat(suggestion2));
            Assert.IsTrue(FormManagementValidation.IsValidFormNameFormat(suggestion3));
        }

        #endregion

        #region Repository Tests (Integration Tests)

        [TestMethod]
        [TestCategory("Integration")]
        public void FormManagementRepository_GetAllForms_ReturnsData()
        {
            try
            {
                // Arrange & Act
                var forms = FormManagementRepository.GetAllForms();

                // Assert
                Assert.IsNotNull(forms, "GetAllForms should return a list");
                Assert.IsTrue(forms.Count >= 0, "Forms list should have zero or more items");
                
                Debug.WriteLine($"Retrieved {forms.Count} forms from database");
            }
            catch (Exception ex)
            {
                Assert.Inconclusive($"Database connection issue: {ex.Message}");
            }
        }

        [TestMethod]
        [TestCategory("Integration")]
        public void FormManagementRepository_GetCategories_ReturnsDistinctCategories()
        {
            try
            {
                // Arrange & Act
                var categories = FormManagementRepository.GetCategories();

                // Assert
                Assert.IsNotNull(categories, "GetCategories should return a list");
                
                Debug.WriteLine($"Retrieved {categories.Count} categories from database");
            }
            catch (Exception ex)
            {
                Assert.Inconclusive($"Database connection issue: {ex.Message}");
            }
        }

        [TestMethod]
        [TestCategory("Integration")]
        public void FormManagementRepository_IsFormNameAvailable_ValidatesUniqueness()
        {
            try
            {
                // Arrange
                string testFormName = "NonExistentFormName_" + Guid.NewGuid().ToString("N").Substring(0, 8);

                // Act
                var isAvailable = FormManagementRepository.IsFormNameAvailable(testFormName);

                // Assert
                Assert.IsTrue(isAvailable, "Non-existent form name should be available");
                
                Debug.WriteLine($"Form name '{testFormName}' availability: {isAvailable}");
            }
            catch (Exception ex)
            {
                Assert.Inconclusive($"Database connection issue: {ex.Message}");
            }
        }

        #endregion

        #region Performance Tests

        [TestMethod]
        [TestCategory("Performance")]
        public void FormManagementValidation_ValidateFormName_Performance()
        {
            // Arrange
            var stopwatch = Stopwatch.StartNew();
            const int iterations = 1000;

            // Act
            for (int i = 0; i < iterations; i++)
            {
                FormManagementValidation.ValidateFormName($"TestForm{i}");
            }
            stopwatch.Stop();

            // Assert
            Assert.IsTrue(stopwatch.ElapsedMilliseconds < 1000, 
                $"Validation should complete in under 1 second for {iterations} iterations. Actual: {stopwatch.ElapsedMilliseconds}ms");
            
            Debug.WriteLine($"Validated {iterations} form names in {stopwatch.ElapsedMilliseconds}ms");
        }

        #endregion
    }
}
