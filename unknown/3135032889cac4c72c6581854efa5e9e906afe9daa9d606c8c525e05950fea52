-- Hard Delete Form
-- Permanently deletes a form from the database
DO $$
DECLARE
    existing_count INTEGER;
    deleted_count INTEGER;
    form_name_val VARCHAR(255);
BEGIN
    -- Set transaction isolation level for consistency
    SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
    
    -- Validate form ID
    IF @form_id IS NULL OR @form_id <= 0 THEN
        RAISE EXCEPTION 'Valid form ID is required';
    END IF;
    
    -- Check if form exists and get form name for confirmation
    SELECT COUNT(*), MAX(form_name) INTO existing_count, form_name_val
    FROM forms 
    WHERE form_id = @form_id;
    
    IF existing_count = 0 THEN
        RAISE EXCEPTION 'Form with ID % does not exist', @form_id;
    END IF;
    
    -- Warning: This will trigger the permission sync function
    -- which will clean up related permission records
    DELETE FROM forms 
    WHERE form_id = @form_id;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Return success message
    SELECT 
        @form_id as form_id,
        form_name_val as form_name,
        'Form deleted successfully. Related permissions have been cleaned up.' as message;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error deleting form: %', SQLERRM;
END $$;
