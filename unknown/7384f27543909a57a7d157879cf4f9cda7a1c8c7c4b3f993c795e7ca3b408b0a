using System;
using System.Windows.Forms;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ProManage.Forms.ReusableForms;

namespace ProManage.Tests.FormManagement
{
    /// <summary>
    /// Tests for MenuRibbon integration in FormManagement form
    /// </summary>
    [TestClass]
    public class MenuRibbonIntegrationTest
    {
        [TestMethod]
        public void MenuRibbon_CanBeInstantiated()
        {
            // Arrange & Act
            MenuRibbon ribbon = null;
            Exception exception = null;

            try
            {
                ribbon = new MenuRibbon();
            }
            catch (Exception ex)
            {
                exception = ex;
            }

            // Assert
            Assert.IsNull(exception, $"MenuRibbon instantiation failed: {exception?.Message}");
            Assert.IsNotNull(ribbon, "MenuRibbon should be instantiated successfully");
            
            // Cleanup
            ribbon?.Dispose();
        }

        [TestMethod]
        public void MenuRibbon_ConfigureForFormManagement_DoesNotThrow()
        {
            // Arrange
            MenuRibbon ribbon = null;
            Exception exception = null;

            try
            {
                ribbon = new MenuRibbon();
                
                // Act
                ribbon.ConfigureForFormType("FormManagement");
            }
            catch (Exception ex)
            {
                exception = ex;
            }

            // Assert
            Assert.IsNull(exception, $"ConfigureForFormType failed: {exception?.Message}");
            
            // Cleanup
            ribbon?.Dispose();
        }

        [TestMethod]
        public void FormManagement_CanBeInstantiated()
        {
            // Arrange & Act
            ProManage.Forms.MainForms.FormManagement form = null;
            Exception exception = null;

            try
            {
                form = new ProManage.Forms.MainForms.FormManagement();
            }
            catch (Exception ex)
            {
                exception = ex;
            }

            // Assert
            Assert.IsNull(exception, $"FormManagement instantiation failed: {exception?.Message}");
            Assert.IsNotNull(form, "FormManagement should be instantiated successfully");

            // Cleanup
            form?.Dispose();
        }

        [TestMethod]
        public void FormManagement_HasRequiredControls()
        {
            // Arrange
            ProManage.Forms.MainForms.FormManagement form = null;

            try
            {
                // Act
                form = new ProManage.Forms.MainForms.FormManagement();

                // Assert
                Assert.IsNotNull(form.gridControl1, "gridControl1 should exist");
                Assert.IsNotNull(form.gridView1, "gridView1 should exist");

                // Check if controls are properly configured
                Assert.AreEqual(DockStyle.Fill, form.gridControl1.Dock, "gridControl1 should be docked to fill");
            }
            finally
            {
                // Cleanup
                form?.Dispose();
            }
        }

        [TestMethod]
        public void MenuRibbon_SetFormNameAndUserId_DoesNotThrow()
        {
            // Arrange
            MenuRibbon ribbon = null;
            Exception exception = null;

            try
            {
                ribbon = new MenuRibbon();
                
                // Act
                ribbon.FormName = "FormManagement";
                ribbon.CurrentUserId = 1;
            }
            catch (Exception ex)
            {
                exception = ex;
            }

            // Assert
            Assert.IsNull(exception, $"Setting FormName and CurrentUserId failed: {exception?.Message}");
            Assert.AreEqual("FormManagement", ribbon.FormName, "FormName should be set correctly");
            Assert.AreEqual(1, ribbon.CurrentUserId, "CurrentUserId should be set correctly");
            
            // Cleanup
            ribbon?.Dispose();
        }
    }
}
