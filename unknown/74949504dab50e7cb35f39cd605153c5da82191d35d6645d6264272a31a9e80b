using System;
using System.Data;
using System.Diagnostics;
using System.Windows.Forms;
using ProManage.Modules.Connections;
using ProManage.Modules.Data.FormManagement;

namespace ProManage.Tests
{
    /// <summary>
    /// Test class to diagnose FormManagement database issues
    /// </summary>
    public static class TestFormManagementDatabase
    {
        /// <summary>
        /// Test database connection and FormManagement data loading
        /// </summary>
        public static void RunDiagnostics()
        {
            try
            {
                Debug.WriteLine("=== FormManagement Database Diagnostics ===");
                
                // Test 1: Database Connection
                TestDatabaseConnection();
                
                // Test 2: Forms Table Existence
                TestFormsTableExists();
                
                // Test 3: Direct SQL Query
                TestDirectSQLQuery();
                
                // Test 4: Repository Method
                TestRepositoryMethod();
                
                Debug.WriteLine("=== Diagnostics Complete ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Diagnostics failed: {ex.Message}");
                MessageBox.Show($"Diagnostics failed: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// Test basic database connection
        /// </summary>
        private static void TestDatabaseConnection()
        {
            try
            {
                Debug.WriteLine("--- Test 1: Database Connection ---");
                
                var connectionManager = DatabaseConnectionManager.Instance;
                Debug.WriteLine($"Connection Status: {connectionManager.IsConnected}");
                Debug.WriteLine($"Last Error: {connectionManager.LastError}");
                
                if (!connectionManager.IsConnected)
                {
                    Debug.WriteLine("Attempting to connect...");
                    bool connected = connectionManager.OpenConnection();
                    Debug.WriteLine($"Connection attempt result: {connected}");
                    
                    if (!connected)
                    {
                        Debug.WriteLine($"Connection failed: {connectionManager.LastError}");
                        return;
                    }
                }
                
                Debug.WriteLine("✅ Database connection successful");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Database connection test failed: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Test if forms table exists
        /// </summary>
        private static void TestFormsTableExists()
        {
            try
            {
                Debug.WriteLine("--- Test 2: Forms Table Existence ---");
                
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    
                    const string checkTableQuery = @"
                        SELECT EXISTS (
                            SELECT 1 FROM information_schema.tables 
                            WHERE table_name = 'forms'
                        )";
                    
                    using (var command = new Npgsql.NpgsqlCommand(checkTableQuery, connection))
                    {
                        bool tableExists = (bool)command.ExecuteScalar();
                        Debug.WriteLine($"Forms table exists: {tableExists}");
                        
                        if (!tableExists)
                        {
                            Debug.WriteLine("❌ Forms table does not exist - run Quick-Fix-FormManagement-Data.sql");
                            return;
                        }
                    }
                    
                    // Check row count
                    const string countQuery = "SELECT COUNT(*) FROM forms";
                    using (var command = new Npgsql.NpgsqlCommand(countQuery, connection))
                    {
                        int rowCount = Convert.ToInt32(command.ExecuteScalar());
                        Debug.WriteLine($"Forms table row count: {rowCount}");
                        
                        if (rowCount == 0)
                        {
                            Debug.WriteLine("⚠️ Forms table is empty - run Quick-Fix-FormManagement-Data.sql");
                        }
                        else
                        {
                            Debug.WriteLine("✅ Forms table has data");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Forms table test failed: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Test direct SQL query
        /// </summary>
        private static void TestDirectSQLQuery()
        {
            try
            {
                Debug.WriteLine("--- Test 3: Direct SQL Query ---");
                
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    
                    const string query = @"
                        SELECT form_id, form_name, display_name, category, is_active, last_modified
                        FROM forms 
                        ORDER BY form_id";
                    
                    using (var command = new Npgsql.NpgsqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            int count = 0;
                            while (reader.Read())
                            {
                                count++;
                                Debug.WriteLine($"Form {count}: ID={reader["form_id"]}, Name={reader["form_name"]}, Display={reader["display_name"]}");
                            }
                            
                            if (count > 0)
                            {
                                Debug.WriteLine($"✅ Direct SQL query returned {count} forms");
                            }
                            else
                            {
                                Debug.WriteLine("⚠️ Direct SQL query returned no data");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Direct SQL query failed: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Test repository method
        /// </summary>
        private static void TestRepositoryMethod()
        {
            try
            {
                Debug.WriteLine("--- Test 4: Repository Method ---");
                
                var forms = FormManagementRepository.GetAllForms();
                Debug.WriteLine($"Repository returned {forms?.Count ?? 0} forms");
                
                if (forms != null && forms.Count > 0)
                {
                    foreach (var form in forms)
                    {
                        Debug.WriteLine($"Form: ID={form.FormId}, Name={form.FormName}, Display={form.DisplayName}");
                    }
                    Debug.WriteLine("✅ Repository method working correctly");
                }
                else
                {
                    Debug.WriteLine("❌ Repository method returned no data");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Repository method failed: {ex.Message}");
            }
        }
    }
}
