-- Update Existing Form
-- Updates an existing form with validation
DO $$
DECLARE
    existing_count INTEGER;
    updated_count INTEGER;
BEGIN
    -- Set transaction isolation level for consistency
    SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
    
    -- Validate required fields
    IF @form_id IS NULL OR @form_id <= 0 THEN
        RAISE EXCEPTION 'Valid form ID is required';
    END IF;
    
    IF @form_name IS NULL OR TRIM(@form_name) = '' THEN
        RAISE EXCEPTION 'Form name is required and cannot be empty';
    END IF;
    
    -- Check if form exists
    SELECT COUNT(*) INTO existing_count
    FROM forms 
    WHERE form_id = @form_id;
    
    IF existing_count = 0 THEN
        RAISE EXCEPTION 'Form with ID % does not exist', @form_id;
    END IF;
    
    -- Check for duplicate form_name (excluding current form)
    SELECT COUNT(*) INTO existing_count
    FROM forms 
    WHERE LOWER(TRIM(form_name)) = LOWER(TRIM(@form_name))
      AND form_id != @form_id;
    
    IF existing_count > 0 THEN
        RAISE EXCEPTION 'A form with the name "%" already exists', @form_name;
    END IF;
    
    -- Validate form_name format
    IF @form_name !~ '^[A-Za-z][A-Za-z0-9_]*$' THEN
        RAISE EXCEPTION 'Form name must start with a letter and contain only letters, numbers, and underscores';
    END IF;
    
    -- Update the form
    UPDATE forms SET
        form_name = TRIM(@form_name),
        display_name = CASE WHEN @display_name IS NULL OR TRIM(@display_name) = '' 
                           THEN NULL 
                           ELSE TRIM(@display_name) 
                      END,
        category = CASE WHEN @category IS NULL OR TRIM(@category) = '' 
                       THEN NULL 
                       ELSE TRIM(@category) 
                  END,
        is_active = COALESCE(@is_active, is_active),
        last_modified = CURRENT_TIMESTAMP
    WHERE form_id = @form_id;
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    IF updated_count = 0 THEN
        RAISE EXCEPTION 'No changes were made to the form';
    END IF;
    
    -- Return the updated form data
    SELECT 
        form_id,
        form_name,
        display_name,
        category,
        is_active,
        last_modified,
        'Form updated successfully' as message
    FROM forms 
    WHERE form_id = @form_id;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error updating form: %', SQLERRM;
END $$;
